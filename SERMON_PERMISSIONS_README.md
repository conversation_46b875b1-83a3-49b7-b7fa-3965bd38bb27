# تحديث صلاحيات إضافة الخطب الجاهزة

## نظرة عامة
تم تطبيق نظام صلاحيات جديد لإضافة الخطب الجاهزة بحيث يُسمح فقط للمستخدمين المؤهلين بإضافة خطب جديدة.

## الأدوار المسموح لها بإضافة الخطب
1. **مشرف المنصة (admin)** - صلاحية كاملة
2. **العالم (scholar)** - يمكنه إضافة الخطب والإجابة على الفتاوى
3. **الخطيب (member)** - يمكنه إضافة الخطب فقط

## التغييرات المطبقة

### 1. تحديث الخادم (Backend)
**الملف:** `routes/sermons.js`
- إضافة التحقق من الدور قبل السماح بإنشاء خطبة جديدة
- رفض الطلبات من المستخدمين غير المؤهلين مع رسالة خطأ واضحة

### 2. تحديث واجهة المستخدم (Frontend)

#### أ. صفحة الخطب الجاهزة (`public/sermons.html`)
- تقسيم قسم إضافة الخطبة إلى ثلاث حالات:
  - **للمستخدمين المؤهلين:** عرض زر إضافة خطبة
  - **للمستخدمين غير المؤهلين:** عرض رسالة توضيحية
  - **للزوار:** عرض أزرار تسجيل الدخول

#### ب. صفحة إضافة الخطبة (`public/add_sermon.html`)
- إضافة حماية للصفحة بالكامل
- منع الوصول للمستخدمين غير المؤهلين
- عرض رسالة خطأ واضحة للمستخدمين غير المؤهلين

### 3. تحديث JavaScript

#### أ. ملف الخطب (`public/js/sermons.js`)
- إضافة دالة `manageAddSermonSection()` لإدارة عرض قسم إضافة الخطبة
- التحقق من دور المستخدم وعرض المحتوى المناسب

#### ب. ملف إضافة الخطبة (`public/js/add-sermon.js`)
- إضافة دالة `checkSermonAddPermission()` للتحقق من الصلاحيات
- إضافة دالة `showAccessDeniedPage()` لعرض صفحة منع الوصول

### 4. تحديث نظام الأذونات (`public/js/auth-protection.js`)
- تحديث أسماء الأدوار لتكون أكثر وضوحاً:
  - `admin`: مشرف المنصة
  - `scholar`: عالم  
  - `member`: خطيب
- إضافة صلاحية `add_sermons` للعلماء والخطباء

### 5. تحديث الأنماط (`public/style.css`)
- إضافة أنماط لرسالة عدم وجود صلاحية
- تحسين مظهر رسائل التنبيه

## كيفية الاختبار

### 1. استخدام ملف الاختبار
افتح الملف `test_sermon_permissions.html` في المتصفح لاختبار الصلاحيات:
- جرب تسجيل الدخول بأدوار مختلفة
- اختبر صلاحية إضافة الخطبة لكل دور
- تصفح صفحات الخطب وإضافة الخطبة

### 2. اختبار يدوي
1. **كزائر:** تصفح صفحة الخطب - يجب أن ترى أزرار تسجيل الدخول
2. **كخطيب:** سجل دخول كعضو - يجب أن ترى زر إضافة خطبة
3. **كعالم:** سجل دخول كعالم - يجب أن ترى زر إضافة خطبة
4. **كمشرف:** سجل دخول كمدير - يجب أن ترى زر إضافة خطبة

## الملفات المعدلة
- `routes/sermons.js`
- `public/sermons.html`
- `public/add_sermon.html`
- `public/js/sermons.js`
- `public/js/add-sermon.js`
- `public/js/auth-protection.js`
- `public/style.css`

## ملفات الاختبار المضافة
- `test_sermon_permissions.html` - ملف اختبار تفاعلي
- `SERMON_PERMISSIONS_README.md` - هذا الملف

## ملاحظات مهمة
1. تأكد من أن نظام المصادقة يعمل بشكل صحيح
2. تأكد من تحديث قاعدة البيانات لتتضمن الأدوار الصحيحة
3. اختبر جميع السيناريوهات قبل النشر في الإنتاج
4. تأكد من أن رسائل الخطأ واضحة ومفيدة للمستخدمين

## التحسينات المستقبلية المقترحة
1. إضافة نظام طلب ترقية الدور للمستخدمين
2. إضافة نظام موافقة على الخطب قبل النشر
3. إضافة إحصائيات لمتابعة نشاط المستخدمين
4. تحسين واجهة إدارة الأذونات
